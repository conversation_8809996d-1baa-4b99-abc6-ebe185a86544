# Algolia to OpenSearch Migration Guide

This document provides a comprehensive guide for migrating from Algolia to OpenSearch in the marketplace application.

## Overview

This migration replaces Algolia search functionality with OpenSearch, providing:

- Self-hosted search solution
- Cost reduction
- Better control over search infrastructure
- Enhanced customization capabilities
- Full-text search with advanced features

## What Changed

### Backend Changes

1. **New OpenSearch Module**: Created `@mercurjs/opensearch` module
2. **Service Layer**: Implemented `OpenSearchModuleService` with similar API to Algolia
3. **Configuration**: Updated `medusa-config.ts` to use OpenSearch
4. **API Routes**: Updated admin API routes from `/admin/algolia` to `/admin/opensearch`
5. **Event Subscribers**: Updated all search-related subscribers
6. **Workflows**: Created OpenSearch-specific workflows
7. **Scripts**: Added migration and sync scripts

### Frontend Changes

1. **Client Library**: Replaced Algolia client with OpenSearch client
2. **Components**: Created new OpenSearch-based components
3. **Search Logic**: Implemented custom search functionality
4. **Environment Variables**: Updated configuration

### Dependencies

**Removed:**
- `algoliasearch`
- `react-instantsearch`
- `react-instantsearch-nextjs`

**Added:**
- `@opensearch-project/opensearch`

## Migration Steps

### 1. Install OpenSearch

#### Using Docker
```bash
docker run -d \
  --name opensearch \
  -p 9200:9200 \
  -p 9600:9600 \
  -e "discovery.type=single-node" \
  -e "OPENSEARCH_INITIAL_ADMIN_PASSWORD=supersecret" \
  opensearchproject/opensearch:latest
```

#### Using Docker Compose
```yaml
version: '3'
services:
  opensearch:
    image: opensearchproject/opensearch:latest
    container_name: opensearch
    environment:
      - discovery.type=single-node
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=supersecret
    ports:
      - "9200:9200"
      - "9600:9600"
```

### 2. Update Environment Variables

#### Backend (.env)
```bash
# Remove Algolia variables
# ALGOLIA_APP_ID=XXX
# ALGOLIA_API_KEY=supersecret

# Add OpenSearch variables
OPENSEARCH_NODE=http://localhost:9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=supersecret
OPENSEARCH_SSL_REJECT_UNAUTHORIZED=false
```

#### Frontend (.env.local)
```bash
# Remove Algolia variables
# NEXT_PUBLIC_ALGOLIA_ID=supersecret
# NEXT_PUBLIC_ALGOLIA_SEARCH_KEY=supersecret

# Add OpenSearch variables
NEXT_PUBLIC_OPENSEARCH_NODE=http://localhost:9200
NEXT_PUBLIC_OPENSEARCH_USERNAME=admin
NEXT_PUBLIC_OPENSEARCH_PASSWORD=supersecret
```

### 3. Install Dependencies

#### Backend
```bash
cd scm-backend
yarn install
```

#### Frontend
```bash
cd scm-frontend
yarn install
```

### 4. Run Migration Script

```bash
cd scm-backend/apps/backend
npm run exec migrate-algolia-to-opensearch
```

### 5. Update Frontend Components

Replace Algolia components with OpenSearch components:

```tsx
// Before
import { AlgoliaProductsListing } from "@/components/sections/ProductListing/AlgoliaProductsListing"

// After
import { OpenSearchProductsListing } from "@/components/sections/ProductListing/OpenSearchProductsListing"
```

### 6. Test the Migration

1. Start OpenSearch
2. Start the backend application
3. Run the migration script
4. Start the frontend application
5. Test search functionality

## Key Differences

### Search Syntax

**Algolia:**
- Used InstantSearch components
- Automatic faceting and filtering
- Built-in UI components

**OpenSearch:**
- Custom search implementation
- Manual query building
- Custom UI components

### Configuration

**Algolia:**
- External service configuration
- JSON-based index settings
- Dashboard management

**OpenSearch:**
- Code-based configuration
- Mapping definitions in service
- API-based management

### Performance

**Algolia:**
- Hosted service with global CDN
- Optimized for speed
- Limited customization

**OpenSearch:**
- Self-hosted with full control
- Customizable performance tuning
- Requires infrastructure management

## Troubleshooting

### Common Issues

1. **OpenSearch Connection Failed**
   - Check if OpenSearch is running
   - Verify connection URL and credentials
   - Check network connectivity

2. **Index Creation Failed**
   - Verify OpenSearch permissions
   - Check index mapping configuration
   - Review OpenSearch logs

3. **Search Results Empty**
   - Run migration script to populate data
   - Check index status in OpenSearch
   - Verify data transformation logic

4. **Frontend Search Not Working**
   - Update environment variables
   - Replace Algolia components with OpenSearch components
   - Check browser console for errors

### Debugging

1. **Check OpenSearch Status**
   ```bash
   curl -X GET "localhost:9200/_cluster/health?pretty"
   ```

2. **List Indices**
   ```bash
   curl -X GET "localhost:9200/_cat/indices?v"
   ```

3. **Check Index Mapping**
   ```bash
   curl -X GET "localhost:9200/products/_mapping?pretty"
   ```

4. **Search Test**
   ```bash
   curl -X GET "localhost:9200/products/_search?pretty" \
     -H 'Content-Type: application/json' \
     -d '{"query": {"match_all": {}}}'
   ```

## Rollback Plan

If issues occur, you can rollback by:

1. Reverting environment variables to Algolia
2. Restoring Algolia dependencies
3. Using original Algolia components
4. Reverting backend configuration

## Performance Optimization

1. **Index Settings**
   - Adjust shard and replica counts
   - Configure refresh intervals
   - Optimize mapping settings

2. **Query Optimization**
   - Use appropriate query types
   - Implement caching
   - Optimize aggregations

3. **Infrastructure**
   - Scale OpenSearch cluster
   - Use SSD storage
   - Configure proper memory settings

## Security Considerations

1. **Authentication**
   - Enable OpenSearch security
   - Use strong passwords
   - Configure user roles

2. **Network Security**
   - Restrict access to OpenSearch
   - Use HTTPS in production
   - Configure firewall rules

3. **Data Protection**
   - Regular backups
   - Encryption at rest
   - Audit logging

## Support

For issues or questions:

1. Check OpenSearch documentation
2. Review application logs
3. Test with sample queries
4. Verify configuration settings
