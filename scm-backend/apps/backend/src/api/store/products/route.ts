import { MedusaRequest, MedusaResponse } from "@medusajs/framework"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

  try {
    // Simple product query without tax calculation
    const { data: products, metadata } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "handle",
        "thumbnail",
        "status",
        "created_at",
        "updated_at",
        "variants.id",
        "variants.title",
        "variants.sku",
        "variants.prices.amount",
        "variants.prices.currency_code",
      ],
      filters: {
        status: "published",
        // Filter out region-specific fields since we're not doing tax calculation
        ...(req.filterableFields && Object.fromEntries(
          Object.entries(req.filterableFields).filter(([key]) =>
            !['region_id', 'country_code'].includes(key)
          )
        )),
      },
      pagination: {
        take: parseInt(req.query.limit as string) || 50,
        skip: parseInt(req.query.offset as string) || 0,
      },
    })

    // Transform products to include calculated_price as regular price to avoid tax calculation
    const transformedProducts = products.map((product: any) => ({
      ...product,
      variants: product.variants?.map((variant: any) => ({
        ...variant,
        calculated_price: variant.prices?.[0] ? {
          calculated_amount: variant.prices[0].amount,
          currency_code: variant.prices[0].currency_code,
          original_amount: variant.prices[0].amount,
          calculated_amount_with_tax: variant.prices[0].amount,
          original_amount_with_tax: variant.prices[0].amount,
        } : null,
      })) || [],
    }))

    // Return products without tax calculation to avoid the tax provider error
    res.json({
      products: transformedProducts,
      count: metadata?.count || 0,
      offset: metadata?.skip || 0,
      limit: metadata?.take || 50,
    })
  } catch (error) {
    console.error("Error fetching products:", error)
    res.status(500).json({
      code: "unknown_error",
      type: "unknown_error",
      message: "An error occurred while fetching products.",
    })
  }
}
