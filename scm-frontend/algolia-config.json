{"settings": {"minWordSizefor1Typo": 4, "minWordSizefor2Typos": 8, "hitsPerPage": 20, "maxValuesPerFacet": 100, "searchableAttributes": ["title", "subtitle", "brand.name", "tags.value", "type.value", "categories.name", "collection.title", "variants.title"], "numericAttributesToIndex": null, "attributesToRetrieve": null, "unretrievableAttributes": null, "optionalWords": null, "attributesForFaceting": ["average_rating", "filterOnly(categories.id)", "categories.name", "seller.handle", "seller.store_status", "filterOnly(supported_countries)", "searchable(title)", "variants.color", "variants.condition", "variants.prices.currency_code", "variants.size"], "attributesToSnippet": null, "attributesToHighlight": null, "paginationLimitedTo": 1000, "attributeForDistinct": null, "exactOnSingleWordQuery": "attribute", "ranking": ["typo", "geo", "words", "filters", "proximity", "attribute", "exact", "custom"], "customRanking": null, "separatorsToIndex": "", "removeWordsIfNoResults": "none", "queryType": "prefixLast", "highlightPreTag": "<em>", "highlightPostTag": "</em>", "alternativesAsExact": ["ignorePlurals", "singleWordSynonym"], "renderingContent": {"facetOrdering": {"facets": {"order": ["variants.color", "variants.size", "variants.condition"]}, "values": {"variants.color": {"sortRemainingBy": "count"}, "variants.condition": {"sortRemainingBy": "count"}, "variants.size": {"sortRemainingBy": "count"}}}}}, "rules": [], "synonyms": []}